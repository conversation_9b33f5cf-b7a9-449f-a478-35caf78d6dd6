import { useState, useEffect } from "react";
import "./App.css";
import { VoicesService } from "./services/VoicesService";
import { AppService } from "./services/AppService";
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";

function App() {
  const [voiceStatus, setVoiceStatus] = useState<string>("No configurado");
  const [audioUrl, setAudioUrl] = useState<string>("");
  const [testText, setTestText] = useState<string>(
    "Ho<PERSON>, este es un texto de prueba para el servicio de voces."
  );

  // Estados para el servicio de IA
  const [aiQuery, setAiQuery] = useState<string>("");
  const [aiResponse, setAiResponse] = useState<string>("");
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGenerated<PERSON>haracter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);

  const voicesService = VoicesService.getInstance();
  const appService = AppService.getInstance();

  // Configurar el callback de audio en el servicio
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      setAudioUrl(audioUrl);
      // Intentar reproducir automáticamente
      setTimeout(() => {
        playAudioWithFallback(audioUrl);
      }, 100);
    });
  }, [appService]);



  // Funciones para el servicio de IA
  const handleGenerateCharacter = async () => {
    setAiLoading(true);
    setAiResponse("");

    try {
      console.log(
        "🔵 Generando personaje con query:",
        GAME_MESSAGES.GENERATE_CHARACTER
      );
      const response = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );
      console.log("🟢 Respuesta recibida:", response);
      console.log("🔍 Claves de la respuesta:", Object.keys(response));

      // Buscar el campo que contiene la respuesta del personaje
      const characterResponse =
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content;

      if (characterResponse) {
        setAiResponse(characterResponse);
        setGeneratedCharacter(characterResponse);
        console.log("✅ Personaje guardado:", characterResponse);
        console.log(
          "✅ Estado actualizado - generatedCharacter:",
          characterResponse
        );
      } else {
        console.error(
          "❌ No se encontró el campo de respuesta. Estructura completa:",
          response
        );
        console.error("❌ Campos disponibles:", Object.keys(response));
        // Mostrar toda la respuesta para debug
        const responseText = JSON.stringify(response, null, 2);
        setAiResponse(`Debug - Respuesta completa: ${responseText}`);

        // Intentar extraer cualquier texto de la respuesta
        const fallbackText =
          response.input || response.query || "Personaje de prueba";
        console.log("🔄 Usando texto de fallback:", fallbackText);
        setGeneratedCharacter(fallbackText);
      }
    } catch (error) {
      console.error("❌ Error generando personaje:", error);
      setAiResponse(
        "Error al generar el personaje. Por favor, inténtalo de nuevo."
      );
    } finally {
      setAiLoading(false);
    }
  };

  const handleStartGame = async () => {
    console.log("🎮 handleStartGame ejecutándose!");
    console.log("🎮 generatedCharacter:", generatedCharacter);

    if (!generatedCharacter.trim()) {
      console.log("❌ No hay personaje generado");
      alert("Primero debes generar un personaje");
      return;
    }

    console.log("🎮 Iniciando juego...");
    setAiLoading(true);
    setAiResponse("");
    setGameStarted(true);

    try {
      // 1. Obtener respuesta del servicio de IA
      const response = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY
      );
      console.log("🎮 Respuesta inicial del juego recibida:", response);

      // 2. El servicio ahora maneja automáticamente el audio y el formato de respuesta
      const responseText = response.response || response.output || response.result || response.text || response.content || 'Respuesta no encontrada';
      console.log("🎮 Respuesta procesada:", responseText);

      // 3. Actualizar estado
      setAiResponse(responseText);
      console.log("🎮 Estado actualizado - gameStarted: true");
    } catch (error) {
      console.error("Error iniciando el juego:", error);
      setAiResponse(
        "Error al iniciar el juego. Por favor, inténtalo de nuevo."
      );
    } finally {
      setAiLoading(false);
    }
  };

  const handleContinueGame = async () => {
    if (!aiQuery.trim()) return;

    setAiLoading(true);

    try {
      // Incluir información del personaje en el contexto
      const queryWithContext = `Personaje a adivinar: ${generatedCharacter}\n\nRespuesta del jugador: ${aiQuery}`;
      const response = await appService.generateWithIaVsPlayer(
        queryWithContext
      );
      // El servicio ahora maneja automáticamente el audio y el formato de respuesta
      const responseText = response.response || response.output || response.result || response.text || response.content || 'Respuesta no encontrada';
      setAiResponse(responseText);
      setAiQuery(""); // Limpiar el campo después de enviar
    } catch (error) {
      console.error("Error continuando el juego:", error);
      setAiResponse(
        "Error al continuar el juego. Por favor, inténtalo de nuevo."
      );
    } finally {
      setAiLoading(false);
    }
  };

  // Funciones para el servicio de voces
  const handleConfigVoice = async () => {
    try {
      setVoiceStatus("Configurando...");
      const success = await voicesService.configVoice("neutral");
      setVoiceStatus(
        success ? "Configurado correctamente" : "Error en la configuración"
      );
    } catch (error) {
      setVoiceStatus("Error en la configuración");
      console.error("Error configurando voz:", error);
    }
  };

  const handleGenerateAudio = async () => {
    try {
      const audioBlob = await voicesService.getAudio(testText);
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);
    } catch (error) {
      console.error("Error generando audio:", error);
    }
  };

  // Variables de entorno
  const config = {
    // Configuración de la API de IA
    llmApiUrl: import.meta.env.VITE_LLM_API_URL,
    llmApiKey: import.meta.env.VITE_LLM_API_KEY,
    llmPresetIdGenCharBot: import.meta.env.VITE_LLM_PRESETID_GENCHARBOT,
    llmPresetIdIaVsPlayer: import.meta.env.VITE_LLM_PRESETID_IA_VS_PLAYER,

    // Configuración de Speech API
    speechApiUrl: import.meta.env.VITE_SPEECH_API_URL,
    speechApiKey: import.meta.env.VITE_SPEECH_API_KEY,

    // Configuración de Movistar Plus API
    movistarApiUrl: import.meta.env.VITE_MOVISTAR_API_URL,

    // Configuración Perplexity
    perplexityId: import.meta.env.VITE_PERPLEXITY_ID,
  };

  return (
    <>
      <h1>Genigma WB - Configuración</h1>
      <div className="card">
        <div
          style={{
            marginTop: "30px",
            padding: "20px",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
        >
          <h3>Servicio de Voces - Prueba</h3>
          <div style={{ marginBottom: "15px" }}>
            <strong>Estado:</strong>{" "}
            <span
              style={{ color: voiceStatus.includes("Error") ? "red" : "green" }}
            >
              {voiceStatus}
            </span>
          </div>

          <div style={{ marginBottom: "15px" }}>
            <button
              onClick={handleConfigVoice}
              style={{ marginRight: "10px", padding: "8px 16px" }}
            >
              Configurar Voz
            </button>
            <button
              onClick={handleGenerateAudio}
              style={{ padding: "8px 16px" }}
            >
              Generar Audio
            </button>
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", marginBottom: "5px" }}>
              Texto para convertir a audio:
            </label>
            <textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              style={{ width: "100%", height: "60px", padding: "8px" }}
            />
          </div>

          {audioUrl && (
            <div>
              <audio controls src={audioUrl} style={{ width: "100%" }}>
                Tu navegador no soporta el elemento de audio.
              </audio>
            </div>
          )}
        </div>

        <div
          style={{
            marginTop: "30px",
            padding: "20px",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
        >
          <h3>Juego de Adivinanza de Personajes</h3>

          {/* Paso 1: Generar Personaje */}
          <div
            style={{
              marginBottom: "20px",
              padding: "15px",
              backgroundColor: "#f0f8ff",
              borderRadius: "8px",
            }}
          >
            <h4 style={{ color: "#007bff" }}>Paso 1: Generar Personaje</h4>
            <p style={{ color: "#333" }}>
              Primero, genera un personaje que la IA tendrá que adivinar:
            </p>

            {/* Debug info */}
            <div
              style={{ fontSize: "12px", color: "#666", marginBottom: "10px" }}
            >
              <strong>Debug:</strong> generatedCharacter = "{generatedCharacter}
              " | aiLoading = {aiLoading.toString()} | aiResponse length ={" "}
              {aiResponse.length} | gameStarted = {gameStarted.toString()}
              <br />
              <strong>Config:</strong> URL = {config.llmApiUrl || "NO_SET"} |
              Key = {config.llmApiKey ? "SET" : "NO_SET"} | Preset ={" "}
              {config.llmPresetIdGenCharBot || "NO_SET"}
              <br />
              <strong>Condición botón:</strong> generatedCharacter.length ={" "}
              {generatedCharacter.length} | !gameStarted ={" "}
              {(!gameStarted).toString()} | Mostrar botón ={" "}
              {(generatedCharacter && !gameStarted).toString()}
            </div>
            <button
              onClick={handleGenerateCharacter}
              disabled={aiLoading}
              style={{
                padding: "10px 20px",
                backgroundColor: aiLoading ? "#ccc" : "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: aiLoading ? "not-allowed" : "pointer",
                marginRight: "10px",
              }}
            >
              {aiLoading ? "Generando..." : "Generar Personaje"}
            </button>

            {/* Botón de debug temporal */}
            <button
              onClick={() => {
                console.log("🔧 Debug: Forzando personaje de prueba");
                setGeneratedCharacter("Personaje de prueba para debug");
                setGameStarted(false);
              }}
              style={{
                padding: "10px 20px",
                backgroundColor: "#ffc107",
                color: "black",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              🔧 Debug: Forzar Personaje
            </button>

            {generatedCharacter ? (
              <div
                style={{
                  marginTop: "10px",
                  padding: "10px",
                  color: "#155724",
                  backgroundColor: "#d4edda",
                  border: "1px solid #c3e6cb",
                  borderRadius: "4px",
                }}
              >
                <strong>Personaje generado:</strong> {generatedCharacter}
              </div>
            ) : (
              aiResponse &&
              !aiLoading && (
                <div
                  style={{
                    marginTop: "10px",
                    padding: "10px",
                    color: "#721c24",
                    backgroundColor: "#f8d7da",
                    border: "1px solid #f5c6cb",
                    borderRadius: "4px",
                  }}
                >
                  <strong>Estado:</strong> {aiResponse}
                </div>
              )
            )}
          </div>

          {/* Paso 2: Iniciar Juego */}
          {generatedCharacter && !gameStarted && (
            <div
              style={{
                marginBottom: "20px",
                padding: "15px",
                color: "black",
                backgroundColor: "#fff3cd",
                borderRadius: "8px",
              }}
            >
              <h4>Paso 2: Iniciar el Juego</h4>
              <p>
                Ahora inicia el juego donde la IA intentará adivinar tu
                personaje:
              </p>
              <button
                onClick={handleStartGame}
                disabled={aiLoading}
                style={{
                  padding: "10px 20px",
                  backgroundColor: aiLoading ? "#ccc" : "#007bff",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: aiLoading ? "not-allowed" : "pointer",
                }}
              >
                {aiLoading ? "Iniciando..." : "Iniciar Juego"}
              </button>
            </div>
          )}

          {/* Paso 3: Continuar Juego */}
          {gameStarted && (
            <div
              style={{
                marginBottom: "20px",
                padding: "15px",
                backgroundColor: "#e2e3e5",
                borderRadius: "8px",
              }}
            >
              <h4>Paso 3: Responder a la IA</h4>
              <p>
                Responde a las preguntas de la IA (responde con "sí", "no", "a
                veces", etc.):
              </p>

              <div style={{ marginBottom: "15px" }}>
                <textarea
                  value={aiQuery}
                  onChange={(e) => setAiQuery(e.target.value)}
                  style={{ width: "100%", height: "60px", padding: "8px" }}
                  placeholder="Escribe tu respuesta aquí..."
                />
              </div>

              <button
                onClick={handleContinueGame}
                disabled={aiLoading || !aiQuery.trim()}
                style={{
                  padding: "10px 20px",
                  backgroundColor: aiLoading ? "#ccc" : "#17a2b8",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: aiLoading ? "not-allowed" : "pointer",
                }}
              >
                {aiLoading ? "Enviando..." : "Enviar Respuesta"}
              </button>
            </div>
          )}

          {/* Respuesta de la IA */}
          {aiResponse && (
            <div
              style={{
                marginTop: "15px",
                padding: "15px",
                color: "#0c5460",
                backgroundColor: "#f8f9fa",
                border: "1px solid #dee2e6",
                borderRadius: "4px",
              }}
            >
              <h4>Respuesta de la IA:</h4>
              <p style={{ whiteSpace: "pre-wrap", margin: "10px 0 0 0" }}>
                {aiResponse}
              </p>

              {/* Reproductor de audio automático */}
              {audioUrl && gameStarted && (
                <div style={{ marginTop: "15px" }}>
                  <h5 style={{ color: "#0c5460" }}>
                    🔊 Audio de la respuesta:
                  </h5>
                  <audio
                    controls
                    src={audioUrl}
                    autoPlay
                    style={{ width: "100%" }}
                    onLoadedData={(e) => {
                      console.log("🎵 Audio cargado, intentando reproducir...");
                      const audioElement = e.target as HTMLAudioElement;
                      audioElement.play().catch((error) => {
                        console.warn(
                          "⚠️ Autoplay bloqueado por el navegador:",
                          error
                        );
                      });
                    }}
                  >
                    Tu navegador no soporta el elemento de audio.
                  </audio>
                  <div style={{ marginTop: "10px" }}>
                    <button
                      onClick={() => {
                        const audioElement = document.querySelector(
                          "audio"
                        ) as HTMLAudioElement;
                        if (audioElement) {
                          audioElement.currentTime = 0;
                          audioElement.play().catch((error) => {
                            console.error("Error reproduciendo audio:", error);
                          });
                        }
                      }}
                      style={{
                        padding: "5px 10px",
                        backgroundColor: "#17a2b8",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer",
                        fontSize: "12px",
                      }}
                    >
                      🔊 Reproducir Audio
                    </button>
                    <p
                      style={{
                        fontSize: "12px",
                        color: "#666",
                        marginTop: "5px",
                      }}
                    >
                      💡 Si el audio no se reproduce automáticamente, usa el
                      botón de arriba
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Botón para reiniciar */}
          {(generatedCharacter || gameStarted) && (
            <div style={{ marginTop: "20px", textAlign: "center" }}>
              <button
                onClick={() => {
                  setGeneratedCharacter("");
                  setGameStarted(false);
                  setAiResponse("");
                  setAiQuery("");
                }}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#6c757d",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Reiniciar Juego
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default App;
