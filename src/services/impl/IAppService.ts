import type { GenerateResponse } from "../../types/GenerateResponse";
import type { SelectedPresetResponse } from "../../types/SelectedPresetResponse";

export interface IAppService {
    setSesid(id: string): void;
    setPreset(preset: 'gencharbot' | 'ia_vs_player'): Promise<void>;
    generate(text: string, id?: string): Promise<GenerateResponse>;
    selectPreset(): Promise<SelectedPresetResponse>;
    setAudioCallback(callback: (audioUrl: string) => void): void;
}
